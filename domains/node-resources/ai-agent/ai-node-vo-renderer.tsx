import classNames from 'classnames';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { spaceHomeTabs } from '@bika/contents/config/client/ai/launcher/tabs';
import { useLocale } from '@bika/contents/i18n';
import { SpaceLauncherDownArea } from '@bika/domains/ai/client/launcher/space-home/space-home-down-area';
import {
  AIWizardWithWelcome,
  type IAIChatWithWelcomeHandle,
} from '@bika/domains/ai/client/wizard/ai-wizard-with-welcome';
import { NodeVOMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-menu';
import { NodeHeaderTitle } from '@bika/domains/node/client/header/node-header-title-component';
import type { AIIntentParams, LauncherTabType } from '@bika/types/ai/bo';
import { useSpaceContext } from '@bika/types/space/context';
import { But<PERSON>, IconButton } from '@bika/ui/button-component';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
// wrench
// import WrenchOutlined from '@bika/ui/icons/components/wrench_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import { Box, Stack } from '@bika/ui/layout-components';
import { BMenu } from '@bika/ui/menu/bmenu';
import { NodeIcon } from '@bika/ui/node/icon';
import { HeaderPageComponent } from '@bika/ui/web-layout';
import { SkillsetIcons } from './skillset-icons';
import { type IAgentInfo, useAgentInfo } from './use-agent-info';
import { useAIChatCache } from '../../ai/client/chat/hooks/use-ai-chat-cache';

interface Props {
  value: IAgentInfo;
}

// const BUTTON_STYLE = 'bg-[--bg-controls] border p-2 border-[--border-default] rounded cursor-pointer';

const AINodeVORendererComponent = (props: Props) => {
  const { value } = props;
  const { key, name, permission, description, iconInfo } = useAgentInfo(value);

  const spaceContext = useSpaceContext();
  const openSidebar = false;
  const locale = useLocale();
  const { t } = useLocale();

  // const { sharing } = useShareContext();

  const chatRef = React.useRef<IAIChatWithWelcomeHandle>(null);
  const searchParams = useSearchParams();
  const router = useRouter();

  const isAINode = value.type === 'node';
  const isAppBuilder = value.type === 'expert' && value.expertKey === 'builder';
  const isChiefOfStaff = value.type === 'expert' && value.expertKey === 'supervisor';

  // 获取 AI 节点的技能集数据
  const skillsets = React.useMemo(() => {
    if (
      isAINode &&
      value.node.resource &&
      'resourceType' in value.node.resource &&
      value.node.resource.resourceType === 'AI'
    ) {
      return value.node.resource.skillsets;
    }
    return undefined;
  }, [isAINode, value]);

  const [cacheChatId, setCacheChatId] = useAIChatCache({
    type: 'agent',
    agent: isAINode
      ? {
          type: 'node',
          nodeId: key,
        }
      : {
          type: 'expert',
          expertKey: value.expertKey,
        },
    // type: 'ai-agent-node',
    // nodeId: isAINode ? key : `${spaceContext?.data.id}-${key}`,
  });

  const initWizardId = React.useMemo(() => cacheChatId, [cacheChatId]);

  const initAIIntent: AIIntentParams = React.useMemo(() => {
    if (isAINode) {
      return {
        type: 'AI_NODE',
        nodeId: key,
        icon: isAINode ? value.node.icon : undefined,
      };
    }
    if (isAppBuilder) {
      return {
        type: 'BUILD_APP',
        spaceId: spaceContext ? spaceContext.data.id : undefined,
      };
    }
    if (isChiefOfStaff) {
      // TODO: Implement space agent intent
      return {
        type: 'DEBUGGER',
        spaceId: spaceContext ? spaceContext.data.id : undefined,
      };
    }
    throw new Error(`Unsupported AI intent for expert key: ${value.expertKey}`);
  }, [isAINode, isAppBuilder, isChiefOfStaff, key, spaceContext, value.expertKey, value.node?.icon]);

  const launcherTabs: LauncherTabType[] = React.useMemo(() => {
    if (isAppBuilder) return ['TEMPLATES', 'AI_REPLAYS', 'AI_HISTORY'];
    if (isChiefOfStaff) return [...spaceHomeTabs, 'AI_HISTORY'];
    return [];
  }, [isAppBuilder, isChiefOfStaff]);

  const AgentType = React.useMemo(() => {
    if (isAppBuilder) return 'AI_BUILDER';
    if (isChiefOfStaff) return 'AI_SUPERVISOR';
    if (isAINode) return 'AI_NODE';
    return 'AI_COPILOT';
  }, [isAppBuilder, isChiefOfStaff, isAINode]);

  const skillsetIcons = React.useMemo(() => {
    if (skillsets && skillsets.length > 0) {
      return (
        <Stack
          onClick={() => {
            if (isAINode && spaceContext) {
              // 使用资源编辑器打开 AI 节点编辑界面
              spaceContext.showUIDrawer({
                type: 'resource-editor',
                props: {
                  screenType: 'NODE_RESOURCE',
                  resourceType: 'AI',
                  nodeId: value.node.id,
                },
              });
            }
          }}
        >
          <SkillsetIcons skillsets={skillsets} maxDisplay={5} />
        </Stack>
      );
    }
    return undefined;
  }, [skillsets, isAINode, spaceContext, value.node.id]);

  useEffect(() => {
    // 点击对话历史中的一条记录，打开历史数据
    const wizardId = searchParams.get('wizardId');
    if (wizardId) {
      setCacheChatId(wizardId);
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('wizardId');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });
    }
  }, [searchParams, router, setCacheChatId]);

  const isChat = chatRef.current?.stage === 'chat';

  return (
    <>
      <HeaderPageComponent
        header={
          (isAINode || isChat) && (
            <NodeHeaderTitle
              nodeId={key}
              nodeType="AI"
              icon={iconInfo}
              // TODO i18n
              name={name || 'no title'}
              description={description}
              permission={permission?.privilege}
              button={
                <div className="flex flex-row gap-1">
                  {/* New Chat Button */}
                  <Button
                    color={'neutral'}
                    variant={'plain'}
                    disabled={!isChat}
                    onClick={() => {
                      setCacheChatId(undefined);
                      chatRef.current?.setStage('welcome');
                    }}
                    startDecorator={<AddOutlined color="var(--text-secondary)" />}
                  >
                    {t.ai.new_chat}
                  </Button>
                  {isAINode ? (
                    <NodeVOMenu value={value.node} detail={value.node} />
                  ) : (
                    <BMenu
                      items={[
                        [
                          {
                            label: t.global.copilot.history,
                            icon: <HistoryOutlined color={'var(--text-secondary)'} />,
                            onClick: () => {
                              spaceContext?.showUIDrawer({
                                type: 'ai-history',
                                props: {
                                  type: AgentType,
                                },
                              });
                            },
                          },
                        ],
                      ]}
                    />
                  )}
                </div>
              }
            />
          )
        }
      >
        <Box
          sx={{
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            width: '100%',
            height: '100%',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              backgroundImage: 'url(/assets/home/<USER>',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.1,
              width: '100%',
              height: '100%',
              pointerEvents: 'none',
              zIndex: 0,
            },
          }}
        >
          <div
            className={classNames('z-10 flex-1 overflow-auto h-full', {
              'pt-[20vh]': !isChat && !isAINode,
              'overflow-hidden': isAINode,
            })}
          >
            <AIWizardWithWelcome
              ref={chatRef}
              title={
                <Box display="flex" alignItems="center" gap={1}>
                  <NodeIcon value={iconInfo} size={40} title={name} />
                  <div className="truncate">{name}</div>
                </Box>
              }
              description={description}
              forceLocale={locale.lang}
              initAIIntent={initAIIntent}
              displayMode={'VIEW'}
              initWizardId={initWizardId}
              allowContextMenu={['ATTACHMENT']}
              skillsetIcons={skillsetIcons}
              setCacheChatId={(chatId) => {
                setCacheChatId(chatId);
              }}
              customBottom={
                launcherTabs.length > 0 && (
                  <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
                    <SpaceLauncherDownArea
                      mode={'NO_TITLE'}
                      tabs={launcherTabs}
                      wizardDto={{
                        type: AgentType,
                        nodeId: key,
                      }}
                    />
                  </Box>
                )
              }
              // wizardId={typeof chatId === 'string' ? chatId : undefined}
            />
          </div>
          {openSidebar && (
            <div className="py-6 pr-6 w-[400px] h-full">
              <div className="bg-[--bg-surface]  rounded-l-lg w-full h-full relative">
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: '30px',
                    right: '30px',
                  }}
                >
                  <CloseOutlined />
                </IconButton>
              </div>
            </div>
          )}
        </Box>
      </HeaderPageComponent>
    </>
  );
};

export const AINodeVORenderer = React.memo(AINodeVORendererComponent);
