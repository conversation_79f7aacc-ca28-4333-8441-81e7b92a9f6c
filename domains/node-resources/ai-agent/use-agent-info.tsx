import React from 'react';
import { getExpertConfigs } from '@bika/contents/config/client/ai/expert';
import { useLocale } from '@bika/contents/i18n';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import type { NodePrivilegeVO } from '@bika/types/permission/vo';
import type { TalkExpertKey } from '@bika/types/space/bo';

export type IAgentInfo = { type: 'node'; node: NodeDetailVO } | { type: 'expert'; expertKey: TalkExpertKey };

export function useAgentInfo(value: IAgentInfo) {
  const locale = useLocale();
  let key: string;
  let name: string;
  let permission: NodePrivilegeVO | undefined;
  let description: string | undefined;
  let iconInfo: INodeIconValue;
  // let icon: AvatarLogo | null | undefined;
  // let iconType: string | undefined;
  const expertConfigs = React.useMemo(() => getExpertConfigs(locale), [locale]);

  if (value.type === 'node') {
    key = value.node.id;
    name = value.node.name;
    description = value.node.description;
    iconInfo = {
      kind: 'node-resource',
      nodeType: value.node.type,
      customIcon: value.node.icon,
    };
    permission = value.node.permission;
  } else {
    const expertCfg = expertConfigs[value.expertKey];
    key = value.expertKey;
    name = expertCfg.name; // TODO: get expert name from config
    description = expertCfg.description; // TODO: get expert description from config
    iconInfo = { kind: 'expert', expertKey: value.expertKey }; // { kind: 'node-resource', nodeType: expertCfg.nodeType as NodeResourceIconType };
    permission = undefined; // Experts do not have permissions in the same way as nodes
  }
  return React.useMemo(
    () => ({
      key,
      name,
      permission,
      description,
      iconInfo,
      // iconType,
      type: value.type,
    }),
    [key, name, permission, description, iconInfo, value.type],
  );
}
