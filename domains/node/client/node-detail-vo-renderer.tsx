'use client';

import assert from 'assert';
import dynamic from 'next/dynamic';
import React, { useEffect } from 'react';
import { VIDEO_CONFIG } from '@bika/contents/config/client/video-config/index';
import type { ILocaleContext } from '@bika/contents/i18n';
import { useLocale } from '@bika/contents/i18n/context';
import type { DashboardVO } from '@bika/types/dashboard/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { useGlobalContext } from '@bika/types/website/context';
import { Skeleton } from '@bika/ui/skeleton';
import { showGuideVideo } from '../../space/client/sidebar/show-guide-video';
import { HelpVideoIframe } from '../../website/client/help-video/help-video-iframe';

// dynamic放在外面，不放在switch-case內，因為会导致"闪烁"，每次都重新加载的skeleton
const NodeFolderDetailRenderer = dynamic(
  () => import('./node-folder-detail-renderer/index').then((module) => module.NodeFolderDetailRenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_FOLDER_RENDERER'} />,
  },
);
const AutomationDetailRenderer = dynamic(
  () =>
    import('@bika/domains/automation/client/automation-detail-renderer').then(
      (module) => module.AutomationDetailRenderer,
    ),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_AUTOMATION_RENDERER'} />,
  },
);
const TemplateFolderVORenderer = dynamic(
  () => import('./node-template-folder-detail-renderer/index').then((module) => module.NodeTemplateFolderVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_TEMPLATE_RENDERER'} />,
  },
);
const DashboardVORenderer = dynamic(
  () => import('@bika/domains/dashboard/client/dashboard-vo-renderer').then((module) => module.DashboardVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DASHBOARD_RENDERER'} />,
  },
);
const FormNodeDetailVORenderer = dynamic(
  () =>
    import('@bika/domains/form/client/form-node-detail-vo-renderer').then((module) => module.FormNodeDetailVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_FORM_RENDERER'} />,
  },
);
const DatabaseVORenderer = dynamic(
  () =>
    import('@bika/domains/database/client/database/database-vo-renderer').then((module) => module.DatabaseVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DATABASE_RENDERER'} />,
  },
);
const MirrorVORenderer = dynamic(
  () => import('../../mirror/client/mirror-vo-renderer').then((module) => module.MirrorVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_MIRROR_RENDERER'} />,
  },
);
const DocumentVORenderer = dynamic(
  () => import('../../doc/client/document-vo-renderer').then((module) => module.DocumentVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DOCUMENT_RENDERER'} />,
  },
);
const FileNodeVORenderer = dynamic(
  () => import('@bika/domains/attachment/client/file-node-vo-renderer').then((module) => module.FileNodeVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DOCUMENT_RENDERER'} />,
  },
);

const AINodeVORenderer = dynamic(
  () => import('@bika/domains/node-resources/ai-agent/vo-renderer').then((module) => module.AINodeVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DOCUMENT_RENDERER'} />,
  },
);
const AIPageVORenderer = dynamic(
  () => import('@bika/domains/ai/client/ai-page/ai-page-vo-renderer').then((module) => module.AIPageVORenderer),
  {
    ssr: false,
    loading: () => <Skeleton pos={'NODE_DOCUMENT_RENDERER'} />,
  },
);

interface Props {
  value: NodeDetailVO;
  refetch: () => void;
  locale: ILocaleContext;
  params?: {
    spaceId: string;
    // 通常在database路由时，会传入currentViewId，以后看有没有其它地方需要（dashboard wiget? automation action?)
    viewId?: string;
  };
  isTemplatePreview?: boolean;
}

export function NodeDetailVORenderer(props: Props) {
  const { value: nodeDetailVO, refetch, params, isTemplatePreview } = props;
  console.log('NodeDetailVORenderer111111111');
  console.log('NodeDetailVORenderer111111111');
  console.log('NodeDetailVORenderer111111111');

  const { mode } = useGlobalContext();
  const { t } = useLocale();

  const nodeType = nodeDetailVO.type;

  // 检测是否首次访问，如果是则显示引导模态框
  useEffect(() => {
    if (nodeDetailVO.isFirstVisit && !isTemplatePreview) {
      // 获取当前语言的视频配置
      const currentLang = props.locale.lang as keyof typeof VIDEO_CONFIG;
      const videoConfig = VIDEO_CONFIG[currentLang];

      // 检查是否有对应节点类型的配置
      if (videoConfig && videoConfig[nodeType]) {
        const nodeVideoConfig = videoConfig[nodeType];

        showGuideVideo({
          t,
          items: [
            {
              key: nodeType,
              title: nodeVideoConfig.title,
              content: <HelpVideoIframe url={nodeVideoConfig.path} title={nodeVideoConfig.title} />,
            },
          ],
          id: `node-first-visit-${nodeDetailVO.id}`, // 使用节点ID确保唯一性
        });
      }
      // 如果找不到配置，则不触发弹窗
    }
  }, [nodeDetailVO.id]);

  const renderNodeContent = () => {
    switch (nodeType) {
      case 'AUTOMATION': {
        return (
          <AutomationDetailRenderer
            locale={props.locale}
            refetch={refetch}
            value={nodeDetailVO}
            isTemplatePreview={isTemplatePreview}
          />
        );
      }
      case 'FOLDER': {
        return (
          <NodeFolderDetailRenderer params={params!} locale={props.locale} refetch={refetch} value={nodeDetailVO} />
        );
      }
      case 'TEMPLATE': {
        return (
          <TemplateFolderVORenderer params={params!} locale={props.locale} refetch={refetch} value={nodeDetailVO} />
        );
      }
      case 'DASHBOARD': {
        const dashboardResource = props.value.resource as DashboardVO;
        assert(dashboardResource, 'dashboardResource should be defined');
        assert(dashboardResource.widgets, 'dashboardResource.widgets should be defined');

        return (
          <DashboardVORenderer
            locale={props.locale}
            value={nodeDetailVO}
            showCreateButton={true}
            isTemplatePreview={isTemplatePreview}
          />
        );
      }
      case 'FORM': {
        return (
          <FormNodeDetailVORenderer
            params={params!}
            value={props.value}
            locale={props.locale}
            isTemplatePreview={isTemplatePreview}
          />
        );
      }
      case 'MIRROR': {
        return <MirrorVORenderer data={nodeDetailVO} />;
      }
      case 'DOCUMENT': {
        if (mode === 'SPA') {
          return (
            <div className="text-h6 text-[--text-primary] h-full flex items-center justify-center">
              不支持在客户端使用
            </div>
          );
        }
        return <DocumentVORenderer params={params!} value={nodeDetailVO} />;
      }
      case 'FILE': {
        return <FileNodeVORenderer value={nodeDetailVO} />;
      }
      case 'DATABASE': {
        const { value: nodeVO } = props;
        assert(nodeVO.type === 'DATABASE', 'node must be a DATABASE');

        const newParams = { ...props.params!, nodeVO };

        return <DatabaseVORenderer params={newParams} isTemplatePreview={isTemplatePreview} />;
      }
      case 'AI': {
        return <AINodeVORenderer value={{ type: 'node', node: nodeDetailVO }} />;
      }
      case 'PAGE': {
        return <AIPageVORenderer value={nodeDetailVO} />;
      }
      default:
        return <>ERROR: Not Implement Node Type: {nodeType}</>;
    }
  };

  return <>{renderNodeContent()}</>;
}
